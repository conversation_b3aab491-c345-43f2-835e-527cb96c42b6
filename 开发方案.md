# 钓鱼生涯 APP — MVP 开发方案

## 1. 目标与成功指标
- 目标：用 AI 识别渔获、提取鱼主体，完成物种解锁与地区榜单，闭环记录分享
- 指标：Top‑1 识别准确率≥80%；端到端处理<5s；识别失败率<10%；D1 留存≥30%；每周有效投稿≥200

## 2. 核心用户流程
1) 登录 2) 拍照/相册导入（可读 EXIF/GPS） 3) AI 识别+分割输出（物种/置信度/遮罩）
4) 用户确认/纠错 5) 解锁图鉴+成就 6) 发布到地区榜单（可隐私处理）

## 3. 功能范围
- Must: 登录/注册、拍照与上传、AI 识别与分割、物种确认与纠错、图鉴解锁、地区榜单（周/月×物种/重量/长度）、基础成就（首获/多种/连续打卡）、隐私开关、埋点与监控
- Should: 多张图批量导入、离线草稿、异常/反作弊检测、内容审核
- Could: 天气/潮汐联动、活动赛季、装备/饵料记录、队伍/俱乐部

## 4. 技术架构
- 客户端：React Native（Expo）
- 后端：golang gin
- 数据库：Postgres + PostGIS（空间索引与地区聚合）
- 对象存储：原图/裁剪图/分割遮罩
- AI 服务：托管视觉分类+实例分割 API；低置信度入复核队列；后续可微调
- 异步与任务：消息队列（识别/分割/指纹/审核异步化）
- 监控：APM、日志、指标（识别准确率、时延、失败率）

## 5. 数据模型（简化）
- User{id, nickname, avatar, region_pref, created_at}
- Species{id, common_name, latin_name, aliases[], icon_url}
- Catch{id, user_id, species_id?, photo_url, mask_url?, cropped_url?, confidence, taken_at, created_at, geo(point), weight?, length?, source_exif jsonb, privacy:enum}
- Region{id, name, code, geom}
- Leaderboard{id, region_id, period:enum(week|month), metric:enum(species|weight|length), start_at, end_at}
- Achievement{id, code, name, desc, icon} / UserAchievement{user_id, achievement_id, unlocked_at}
- ReviewTask{id, catch_id, status:enum(pending|done), suggested_species[], reviewer_note}
- MediaFingerprint{id, catch_id, phash, exif_hash, created_at}

## 6. 地区划分与榜单规则
- 地区：行政区+PostGIS 多边形匹配，缺省按地理网格回退
- 榜单：周期=周/月；指标=物种数（解锁计数）、最大重量、最大长度
- 去重与反作弊：感知哈希近重、EXIF 时间/设备一致性校验、异常地理跳跃、重复投稿拦截；异常进入复核

## 7. AI 识别与分割
- 输入：原图 URL（含可选 EXIF/GPS）
- 输出：species_candidates[{id, name, prob}], best_species, confidence, mask_url（PNG/RLE），cropped_url
- 阈值：T_high=0.85 自动通过；T_low=0.6 以下进入复核；中间区用户二次确认
- 多实例：实例分割返回多遮罩，默认选面积最大+居中优先；允许用户切换
- 纠错：用户从 Top‑3 候选/搜索纠正，写入 ReviewTask 及训练池

## 8. API 设计（示例）
- POST /auth/signup, /auth/login
- POST /catch: 上传签名→回传文件→创建记录（pending）
- POST /ai/identify: {photo_url, exif?} → {best_species, confidence, mask_url}
- PATCH /catch/:id/confirm: {species_id, weight?, length?, privacy}
- GET /species?query=
- GET /dex/me: 我的图鉴/解锁进度
- GET /leaderboard?region=xx&period=week&metric=species
- GET /me/catches, GET /catch/:id

## 9. 客户端页面
- 登录/注册、拍照与相册、渔获详情（确认/纠错）、图鉴（已解锁/未解锁）、榜单（地区切换/周期切换）、个人页（成就/草稿/隐私设置）

## 10. 隐私与安全
- 位置精度开关：精确/模糊（栅格化）/隐藏
- 元数据处理：EXIF 可选剥离对外展示，内部留存用于校验
- 内容安全：图片审核（涉敏/违规）、用户举报与封禁
- 数据保护：最小化采集，分级存取，脱敏日志

## 11. 埋点与监控
- 事件：启动、拍照、上传成功、AI成功/失败、确认/纠错、发布、解锁、分享、进入榜单
- 指标：识别准确率、处理时延、失败率、留存、转化（拍照→发布）、复核量
- 告警：识别失败率>阈值、时延>阈值、队列积压、异常增长

## 12. 测试与质量
- 单元：服务层/规则引擎/坐标与地区匹配
- 集成：上传→识别→确认→发布链路
- 模型评测：物种验证集 Top‑1/Top‑3，分割 IoU
- 手工回归：关键机型拍照/权限/相册/弱网/离线草稿

## 13. 里程碑（6 周）
- W1：需求冻结、信息架构、数据模型、RN 项目初始化、上传链路打通
- W2：AI 接入与异步队列、识别与分割闭环、基础页面骨架
- W3：确认/纠错、图鉴与成就（基础3‑5个）、地区匹配
- W4：榜单与反作弊首版、埋点与监控、内容审核
- W5：性能优化、弱网与离线、隐私开关、测试与修复
- W6：灰度发布（5%→20%）、反馈收集与快修、上线

## 14. 风险与应对
- 识别偏差：人工复核与阈值保护、持续收集纠错样本
- 分割失败：回退中心裁剪与手动框选
- 作弊：感知哈希+EXIF 校验+人工审核多策略并用
- 法规与隐私：默认模糊坐标、清晰位置二次确认提示

## 15. 未来迭代
- 天气/潮汐/水域数据关联、赛季与活动、装备/饵料记录、社交与队伍、个性化物种学习与离线包
